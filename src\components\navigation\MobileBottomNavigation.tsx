'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  FileText,
  Inbox,
  MessageCircle,
  Menu,
  User,
  Settings,
  DollarSign,
  LogOut,
  CheckCircle,
  Star,
} from 'lucide-react';
import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';

interface MobileBottomNavigationProps {
  userType: 'influencer' | 'business';
}

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
}

interface MenuNavItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
}

export function MobileBottomNavigation({
  userType,
}: MobileBottomNavigationProps) {
  const pathname = usePathname();
  const { signOut } = useAuth();
  const router = useRouter();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  // Glavne 4 ikonice za influencere
  const influencerMainNav: NavItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard/influencer',
      icon: LayoutDashboard,
      label: 'Home',
    },
    {
      name: 'Kampanje',
      href: '/marketplace/campaigns',
      icon: FileText,
      label: 'Prilike',
    },
    {
      name: 'Ponude',
      href: '/dashboard/influencer/offers',
      icon: Inbox,
      label: 'Ponude',
    },
    {
      name: 'Poruke',
      href: '/dashboard/chat',
      icon: MessageCircle,
      label: 'Poruke',
    },
  ];

  // Glavne 4 ikonice za biznis korisnike
  const businessMainNav: NavItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard/biznis',
      icon: LayoutDashboard,
      label: 'Home',
    },
    {
      name: 'Kampanje',
      href: '/dashboard/campaigns',
      icon: FileText,
      label: 'Kampanje',
    },
    {
      name: 'Aplikacije',
      href: '/dashboard/biznis/applications',
      icon: Inbox,
      label: 'Aplikacije',
    },
    {
      name: 'Poruke',
      href: '/dashboard/chat',
      icon: MessageCircle,
      label: 'Poruke',
    },
  ];

  // Ostali elementi za hamburger meni - influenceri
  const influencerMenuNav: MenuNavItem[] = [
    {
      name: 'Moj račun',
      href: '/dashboard/influencer/account',
      icon: User,
      description: 'Lični podaci i sigurnost',
    },
    {
      name: 'Postavke profila',
      href: '/dashboard/influencer/profile',
      icon: Settings,
      description: 'Javni profil i cijene',
    },
    {
      name: 'Zarada',
      href: '/dashboard/influencer/earnings',
      icon: DollarSign,
      description: 'Pregled zarade i isplate',
    },
    {
      name: 'Završeni poslovi',
      href: '/dashboard/influencer/zavrseni-poslovi',
      icon: CheckCircle,
      description: 'Praćenje završenih poslova',
    },
  ];

  // Ostali elementi za hamburger meni - biznis
  const businessMenuNav: MenuNavItem[] = [
    {
      name: 'Moj račun',
      href: '/dashboard/biznis/account',
      icon: User,
      description: 'Podaci o firmi i sigurnost',
    },
    {
      name: 'Postavke profila',
      href: '/dashboard/biznis/profile',
      icon: Settings,
      description: 'Javni profil firme',
    },
    {
      name: 'Influenceri',
      href: '/marketplace/influencers',
      icon: User,
      description: 'Pronađi influencere',
    },
    {
      name: 'Završeni poslovi',
      href: '/dashboard/biznis/zavrseni-poslovi',
      icon: CheckCircle,
      description: 'Praćenje završenih poslova',
    },
  ];

  const mainNavigation =
    userType === 'influencer' ? influencerMainNav : businessMainNav;
  const menuNavigation =
    userType === 'influencer' ? influencerMenuNav : businessMenuNav;

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/');
      setIsMenuOpen(false);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const isActive = (href: string) => {
    return pathname === href || pathname.startsWith(href + '/');
  };

  return (
    <>
      {/* Mobile Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 z-50 bg-card border-t border-border md:hidden">
        <div className="flex items-center justify-around py-2">
          {/* Glavne 4 ikonice */}
          {mainNavigation.map(item => (
            <Link key={item.name} href={item.href}>
              <div
                className={cn(
                  'flex flex-col items-center justify-center p-2 min-w-[60px] rounded-lg transition-colors',
                  isActive(item.href)
                    ? 'text-primary bg-primary/10'
                    : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                )}
              >
                <item.icon className="h-5 w-5 mb-1" />
                <span className="text-xs font-medium">{item.label}</span>
              </div>
            </Link>
          ))}

          {/* Hamburger Menu */}
          <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
            <SheetTrigger asChild>
              <div className="flex flex-col items-center justify-center p-2 min-w-[60px] rounded-lg transition-colors text-muted-foreground hover:text-foreground hover:bg-accent cursor-pointer">
                <Menu className="h-5 w-5 mb-1" />
                <span className="text-xs font-medium">Više</span>
              </div>
            </SheetTrigger>
            <SheetContent side="bottom" className="h-auto max-h-[80vh]">
              <SheetHeader>
                <SheetTitle>Meni</SheetTitle>
              </SheetHeader>

              <div className="grid gap-4 py-4">
                {menuNavigation.map(item => (
                  <Link
                    key={item.name}
                    href={item.href}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <div
                      className={cn(
                        'flex items-center space-x-3 p-3 rounded-lg transition-colors',
                        isActive(item.href)
                          ? 'bg-primary text-primary-foreground'
                          : 'hover:bg-accent'
                      )}
                    >
                      <item.icon className="h-5 w-5" />
                      <div className="flex-1">
                        <div className="font-medium">{item.name}</div>
                        {item.description && (
                          <div className="text-sm opacity-75 mt-0.5">
                            {item.description}
                          </div>
                        )}
                      </div>
                    </div>
                  </Link>
                ))}

                {/* Odjava */}
                <Button
                  variant="ghost"
                  onClick={handleSignOut}
                  className="flex items-center justify-start space-x-3 p-3 w-full text-destructive hover:text-destructive hover:bg-destructive/10"
                >
                  <LogOut className="h-5 w-5" />
                  <span className="font-medium">Odjava</span>
                </Button>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      {/* Spacer za bottom navigation */}
      <div className="h-16 md:hidden" />
    </>
  );
}
