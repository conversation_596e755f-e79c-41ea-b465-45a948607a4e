'use client';

import { usePathname, useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  User,
  Settings,
  Building2,
  Users,
  FileText,
  MessageCircle,
  DollarSign,
  LogOut,
  Menu,
  Send,
  Inbox,
  CheckCircle,
  Star,
} from 'lucide-react';
import { useState } from 'react';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';

interface SidebarItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
}

interface DesktopNavigationProps {
  userType: 'influencer' | 'business';
  className?: string;
}

export function DesktopNavigation({
  userType,
  className,
}: DesktopNavigationProps) {
  const pathname = usePathname();
  const { signOut } = useAuth();
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);

  // Navigacija za influencer korisnike
  const influencerNavigation: SidebarItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard/influencer',
      icon: LayoutDashboard,
      description: 'Pregled aktivnosti i statistika',
    },
    {
      name: 'Moj račun',
      href: '/dashboard/influencer/account',
      icon: User,
      description: 'Lični podaci i sigurnost',
    },
    {
      name: 'Postavke profila',
      href: '/dashboard/influencer/profile',
      icon: Settings,
      description: 'Javni profil i cijene',
    },
    {
      name: 'Kampanje',
      href: '/marketplace/campaigns',
      icon: FileText,
      description: 'Dostupne kampanje',
    },
    {
      name: 'Ponude i aplikacije',
      href: '/dashboard/influencer/offers',
      icon: Inbox,
      description: 'Direktne ponude i aplikacije',
    },
    {
      name: 'Poruke',
      href: '/dashboard/chat',
      icon: MessageCircle,
      description: 'Komunikacija sa brendovima',
    },
    {
      name: 'Zarada',
      href: '/dashboard/influencer/earnings',
      icon: DollarSign,
      description: 'Pregled zarade i isplate',
    },
    {
      name: 'Završeni poslovi',
      href: '/dashboard/influencer/zavrseni-poslovi',
      icon: CheckCircle,
      description: 'Praćenje završenih poslova',
    },
  ];

  // Navigacija za biznis korisnike
  const businessNavigation: SidebarItem[] = [
    {
      name: 'Dashboard',
      href: '/dashboard/biznis',
      icon: LayoutDashboard,
      description: 'Pregled kampanja i statistika',
    },
    {
      name: 'Moj račun',
      href: '/dashboard/biznis/account',
      icon: Building2,
      description: 'Podaci o firmi i sigurnost',
    },
    {
      name: 'Postavke profila',
      href: '/dashboard/biznis/profile',
      icon: Settings,
      description: 'Javni profil firme',
    },
    {
      name: 'Moje kampanje',
      href: '/dashboard/campaigns',
      icon: FileText,
      description: 'Upravljanje kampanjama',
    },
    {
      name: 'Aplikacije',
      href: '/dashboard/biznis/applications',
      icon: FileText,
      description: 'Aplikacije na kampanje',
    },
    {
      name: 'Moje ponude',
      href: '/dashboard/biznis/offers',
      icon: Send,
      description: 'Direktne ponude influencerima',
    },
    {
      name: 'Influenceri',
      href: '/marketplace/influencers',
      icon: Users,
      description: 'Pronađi influencere',
    },
    {
      name: 'Poruke',
      href: '/dashboard/chat',
      icon: MessageCircle,
      description: 'Komunikacija sa influencerima',
    },
    {
      name: 'Završeni poslovi',
      href: '/dashboard/biznis/zavrseni-poslovi',
      icon: CheckCircle,
      description: 'Praćenje završenih poslova',
    },
  ];

  const navigation =
    userType === 'influencer' ? influencerNavigation : businessNavigation;

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/');
      setIsOpen(false);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <div className={cn('hidden md:block', className)}>
      {/* Desktop Navigation Trigger */}
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button
            variant="outline"
            size="icon"
            className="fixed top-4 left-4 z-40 bg-card border-border shadow-lg hover:bg-accent"
          >
            <Menu className="h-4 w-4" />
            <span className="sr-only">Otvori navigaciju</span>
          </Button>
        </SheetTrigger>

        <SheetContent side="left" className="w-80 p-0">
          <div className="flex flex-col h-full">
            {/* Header */}
            <SheetHeader className="p-6 border-b border-border">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <span className="text-primary-foreground font-bold text-lg">
                    🔗
                  </span>
                </div>
                <SheetTitle className="text-lg font-bold text-foreground">
                  InfluConnect
                </SheetTitle>
              </div>
            </SheetHeader>

            {/* Navigation */}
            <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
              {navigation.map(item => {
                const isActive =
                  pathname === item.href ||
                  pathname.startsWith(item.href + '/');

                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    onClick={() => setIsOpen(false)}
                  >
                    <div
                      className={cn(
                        'group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-colors',
                        isActive
                          ? 'bg-primary text-primary-foreground'
                          : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                      )}
                    >
                      <item.icon className="flex-shrink-0 h-5 w-5 mr-3" />
                      <div className="flex-1">
                        <div className="font-medium">{item.name}</div>
                        {item.description && (
                          <div className="text-xs opacity-75 mt-0.5">
                            {item.description}
                          </div>
                        )}
                      </div>
                    </div>
                  </Link>
                );
              })}
            </nav>

            {/* Footer */}
            <div className="p-4 border-t border-border">
              <Button
                variant="ghost"
                onClick={handleSignOut}
                className="w-full justify-start text-destructive hover:text-destructive hover:bg-destructive/10"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Odjava
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
}
