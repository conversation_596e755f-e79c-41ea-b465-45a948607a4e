'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  getInfluencer,
  getInfluencerCategories,
  getInfluencerPlatforms,
  getInfluencerPricing,
} from '@/lib/profiles';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Loader2, DollarSign, TrendingUp, Star, Edit } from 'lucide-react';

export default function InfluencerDashboardPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [influencer, setInfluencer] = useState<any>(null);
  const [categories, setCategories] = useState<any[]>([]);
  const [platforms, setPlatforms] = useState<any[]>([]);
  const [pricing, setPricing] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadInfluencer();
    }
  }, [user]);

  const loadInfluencer = async () => {
    try {
      setLoading(true);
      const { data, error } = await getInfluencer(user!.id);

      if (error || !data) {
        // Influencer profile doesn't exist, redirect to profile creation
        router.push('/profil/kreiranje/influencer');
        return;
      }

      setInfluencer(data);

      // Load categories
      const { data: categoriesData } = await getInfluencerCategories(user!.id);
      setCategories(categoriesData || []);

      // Load platforms
      const { data: platformsData } = await getInfluencerPlatforms(user!.id);
      setPlatforms(platformsData || []);

      // Load pricing
      const { data: pricingData } = await getInfluencerPricing(user!.id);
      setPricing(pricingData || []);
    } catch (err) {
      console.error('Error loading influencer data:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredUserType="influencer">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="influencer">
      <div className="space-y-8">
        {/* Welcome Section */}
        <div>
          <h1 className="text-3xl font-bold text-foreground mb-2">
            Dobrodošli, {influencer?.profiles?.username || 'Influencer'}!
          </h1>
          <p className="text-muted-foreground">
            Evo pregleda vaših aktivnosti na platformi
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Ukupna zarada
              </CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">0 KM</div>
              <p className="text-xs text-muted-foreground">
                +0% od prošlog mjeseca
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Aktivne kampanje
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">0</div>
              <p className="text-xs text-muted-foreground">
                Trenutno nema aktivnih kampanja
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Ocjena</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">-</div>
              <p className="text-xs text-muted-foreground">
                Nema ocjena još uvijek
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Profile Summary */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Vaš profil</CardTitle>
              <CardDescription>
                Pregled vaših informacija i društvenih mreža
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/dashboard/influencer/profile')}
            >
              <Edit className="mr-2 h-4 w-4" />
              Uredi profil
            </Button>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Categories */}
            {categories.length > 0 && (
              <div>
                <h4 className="font-medium mb-2">Kategorije</h4>
                <div className="flex flex-wrap gap-2">
                  {categories.map((cat: any) => (
                    <span
                      key={cat.category_id}
                      className="inline-flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary rounded-md text-sm"
                    >
                      <span>{cat.categories?.icon}</span>
                      <span>{cat.categories?.name}</span>
                      {cat.is_primary && (
                        <span className="text-xs bg-primary text-primary-foreground px-1 rounded">
                          Glavna
                        </span>
                      )}
                    </span>
                  ))}
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2">Osnovne informacije</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>
                    <strong>Username:</strong> @{influencer?.profiles?.username}
                  </li>
                  <li>
                    <strong>Lokacija:</strong>{' '}
                    {influencer?.profiles?.location || 'Nije navedeno'}
                  </li>
                  {influencer?.gender && (
                    <li>
                      <strong>Pol:</strong>{' '}
                      {influencer.gender === 'male'
                        ? 'Muški'
                        : influencer.gender === 'female'
                          ? 'Ženski'
                          : influencer.gender === 'other'
                            ? 'Ostalo'
                            : 'Preferiram da ne kažem'}
                    </li>
                  )}
                  {influencer?.age && (
                    <li>
                      <strong>Godine:</strong> {influencer.age}
                    </li>
                  )}
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">Platforme</h4>
                {platforms.length > 0 ? (
                  <div className="space-y-2">
                    {platforms.map((platform: any) => (
                      <div
                        key={platform.platform_id}
                        className="flex items-center gap-2 text-sm"
                      >
                        <span className="text-lg">
                          {platform.platforms?.icon}
                        </span>
                        <div className="flex-1">
                          <span className="font-medium">
                            {platform.platforms?.name}
                          </span>
                          {platform.handle && (
                            <span className="text-muted-foreground">
                              {' '}
                              • @{platform.handle}
                            </span>
                          )}
                          {platform.followers_count > 0 && (
                            <span className="text-muted-foreground">
                              {' '}
                              • {platform.followers_count.toLocaleString()}{' '}
                              pratilaca
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">
                    Nema dodanih platformi
                  </p>
                )}
              </div>
            </div>

            {pricing.length > 0 && (
              <div>
                <h4 className="font-medium mb-2">Cijene</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 text-sm">
                  {pricing.map((price: any) => (
                    <div
                      key={`${price.platform_id}-${price.content_type_id}`}
                      className="bg-muted/50 p-3 rounded-lg"
                    >
                      <div className="flex items-center gap-2 mb-1">
                        <span className="text-base">
                          {price.platforms?.icon}
                        </span>
                        <span className="font-medium text-xs">
                          {price.platforms?.name}
                        </span>
                      </div>
                      <div className="font-medium">
                        {price.content_types?.name}
                      </div>
                      <div className="text-lg font-bold text-primary">
                        {price.price} KM
                      </div>
                    </div>
                  ))}
                </div>
                {pricing.length === 0 && (
                  <p className="text-sm text-muted-foreground">
                    Nema postavljenih cijena
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Brze akcije</CardTitle>
            <CardDescription>Šta želite da radite danas?</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button
                className="h-auto p-4 flex flex-col items-start space-y-2"
                onClick={() => router.push('/marketplace/campaigns')}
              >
                <span className="font-medium">Pronađi kampanje</span>
                <span className="text-sm text-muted-foreground">
                  Pregledaj dostupne kampanje za vašu nišu
                </span>
              </Button>
              <Button
                variant="outline"
                className="h-auto p-4 flex flex-col items-start space-y-2"
                onClick={() => router.push('/dashboard/influencer/profile')}
              >
                <span className="font-medium">Uredi profil</span>
                <span className="text-sm text-muted-foreground">
                  Ažuriraj svoje informacije i cijene
                </span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
