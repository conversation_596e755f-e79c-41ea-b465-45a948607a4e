'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Loader2, Plus, X, Info } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import {
  createCampaign,
  addCampaignPlatforms,
  addCampaignCategories,
} from '@/lib/campaigns';
import { supabase } from '@/lib/supabase';
import { getCategories } from '@/lib/categories';
import { getPlatforms } from '@/lib/platforms';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

// Schema za validaciju
const campaignSchema = z.object({
  title: z
    .string()
    .min(5, 'Naziv mora imati najmanje 5 karaktera')
    .max(200, 'Naziv je predugačak'),
  description: z.string().min(20, 'Opis mora imati najmanje 20 karaktera'),
  budget: z
    .number()
    .min(50, 'Minimalni budžet je 50 KM')
    .max(50000, 'Maksimalni budžet je 50,000 KM'),
  contentTypes: z
    .array(z.string())
    .min(1, 'Morate odabrati najmanje jedan tip sadržaja'),
  location: z.string().optional(),
  applicationDeadline: z.string().optional(),
  requirements: z.string().optional(),
  deliverables: z.string().optional(),
  contactEmail: z
    .string()
    .email('Neispravna email adresa')
    .optional()
    .or(z.literal('')),
  contactPhone: z.string().optional(),
  collaborationType: z.enum(['paid', 'barter', 'hybrid']),
  paymentTerms: z.enum(['upfront', '50_50', 'on_delivery']).optional(),
  usageRights: z
    .enum(['single_use', 'unlimited', '6_months', '1_year'])
    .optional(),
  exclusivityPeriod: z.number().min(0).max(365).optional(),
  revisionsIncluded: z.number().min(0).max(10).optional(),
  minFollowers: z.number().min(0).optional(),
  maxFollowers: z.number().min(0).optional(),
  ageRangeMin: z.number().min(13).max(65).optional(),
  ageRangeMax: z.number().min(13).max(65).optional(),
  gender: z.enum(['male', 'female', 'all']).optional(),
  hashtags: z.string().optional(),
  doNotMention: z.string().optional(),
  showBusinessName: z.boolean().default(false),
  isFeatured: z.boolean().default(false),
});

type CampaignForm = z.infer<typeof campaignSchema>;

interface Platform {
  id: number;
  name: string;
  slug: string;
  icon: string;
}

interface Category {
  id: number;
  name: string;
  slug: string;
  icon: string;
}

interface CreateCampaignFormProps {
  onSuccess?: (campaignId: string) => void;
  onCancel?: () => void;
  onSubmit?: (data: any) => void;
  initialData?: any;
  isEditing?: boolean;
  isSubmitting?: boolean;
  submitButtonText?: string;
}

export function CreateCampaignForm({
  onSuccess,
  onCancel,
  onSubmit,
  initialData,
  isEditing = false,
  isSubmitting = false,
  submitButtonText,
}: CreateCampaignFormProps) {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [platforms, setPlatforms] = useState<Platform[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [contentTypes, setContentTypes] = useState<
    { id: string; name: string }[]
  >([]);
  const [selectedPlatforms, setSelectedPlatforms] = useState<number[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<number[]>([]);
  const [selectedContentTypes, setSelectedContentTypes] = useState<string[]>(
    []
  );
  const [currentStep, setCurrentStep] = useState(1);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    setError,
    clearErrors,
    getValues,
  } = useForm<CampaignForm>({
    resolver: zodResolver(campaignSchema),
    defaultValues: {
      ...{
        collaborationType: 'paid',
        paymentTerms: '50_50',
        usageRights: 'single_use',
        revisionsIncluded: 2,
        gender: 'all',
        showBusinessName: false,
        isFeatured: false,
      },
      ...initialData,
    },
  });

  // Load platforms, categories and content types
  useEffect(() => {
    const loadData = async () => {
      try {
        const [platformsResult, categoriesResult] = await Promise.all([
          getPlatforms(),
          getCategories(),
        ]);

        if (platformsResult.data) setPlatforms(platformsResult.data);
        if (categoriesResult.data) setCategories(categoriesResult.data);

        // Load content types from enum
        setContentTypes([
          { id: 'post', name: 'Post' },
          { id: 'story', name: 'Story' },
          { id: 'reel', name: 'Reel' },
          { id: 'video', name: 'Video' },
          { id: 'live', name: 'Live' },
          { id: 'igtv', name: 'IGTV' },
          { id: 'carousel', name: 'Carousel' },
          { id: 'ugc', name: 'UGC' },
        ]);
      } catch (error) {
        console.error('Error loading data:', error);
      }
    };

    loadData();
  }, []);

  // Set initial values for editing mode
  useEffect(() => {
    if (isEditing && initialData) {
      if (initialData.platforms) {
        setSelectedPlatforms(
          initialData.platforms.map((id: string) => parseInt(id))
        );
      }
      if (initialData.categories) {
        setSelectedCategories(
          initialData.categories.map((id: string) => parseInt(id))
        );
      }
      if (initialData.content_types) {
        setSelectedContentTypes(initialData.content_types);
      }
    }
  }, [isEditing, initialData]);

  const handleFormSubmit = async (data: CampaignForm) => {
    console.log('🚀 onSubmit called with data:', data);
    console.log('🚀 selectedPlatforms:', selectedPlatforms);
    console.log('🚀 selectedCategories:', selectedCategories);
    console.log('🚀 user:', user);
    console.log('🚀 currentStep:', currentStep);

    if (!user) {
      console.log('No user found, returning');
      return;
    }

    // Validation
    if (selectedPlatforms.length === 0) {
      console.log('No platforms selected');
      setError('root', { message: 'Morate izabrati najmanje jednu platformu' });
      return;
    }

    if (selectedCategories.length === 0) {
      console.log('No categories selected');
      setError('root', {
        message: 'Morate izabrati najmanje jednu kategoriju',
      });
      return;
    }

    // If editing mode and custom onSubmit provided, use it
    if (isEditing && onSubmit) {
      const formData = {
        ...data,
        platforms: selectedPlatforms.map(id => id.toString()),
        categories: selectedCategories.map(id => id.toString()),
        content_types: selectedContentTypes,
      };
      await onSubmit(formData);
      return;
    }

    console.log('Starting campaign creation...');
    setIsLoading(true);

    try {
      // Create campaign
      const campaignData = {
        business_id: user.id,
        title: data.title,
        description: data.description,
        budget: data.budget,
        // location: data.location || null,
        // application_deadline: data.applicationDeadline || null,
        // requirements: data.requirements || null,
        // deliverables: data.deliverables || null,
        // contact_email: data.contactEmail || null,
        // contact_phone: data.contactPhone || null,
        // collaboration_type: data.collaborationType,
        // payment_terms: data.paymentTerms || null,
        // usage_rights: data.usageRights || null,
        // exclusivity_period: data.exclusivityPeriod || null,
        // revisions_included: data.revisionsIncluded || 2,
        // min_followers: data.minFollowers || null,
        // max_followers: data.maxFollowers || null,
        // age_range_min: data.ageRangeMin || null,
        // age_range_max: data.ageRangeMax || null,
        // gender: data.gender === 'all' ? null : data.gender,
        // hashtags: data.hashtags ? data.hashtags.split(',').map(h => h.trim()) : null,
        // do_not_mention: data.doNotMention ? data.doNotMention.split(',').map(d => d.trim()) : null,
        // show_business_name: data.showBusinessName,
        // is_featured: data.isFeatured,
        status: 'draft' as const,
      };

      console.log('🚀 Sending campaignData:', campaignData);
      console.log('🚀 User ID:', user.id);

      // Check if user exists in businesses table
      const { data: businessCheck, error: businessError } = await supabase
        .from('businesses')
        .select('id')
        .eq('id', user.id)
        .single();

      console.log('🚀 Business check:', businessCheck, businessError);

      // Create campaign using the proper function
      const { data: campaign, error: campaignError } = await createCampaign({
        business_id: user.id,
        title: data.title,
        description: data.description,
        budget: parseFloat(data.budget.toString()),
        content_types: data.contentTypes,
        status: 'draft',
      });

      console.log('🚀 Campaign creation result:', campaign, campaignError);

      if (campaignError) {
        console.error('🚨 Campaign creation error:', campaignError);
        setError('root', {
          message: 'Greška pri kreiranju kampanje: ' + campaignError.message,
        });
        return;
      }

      if (!campaign) {
        setError('root', { message: 'Kampanja nije kreirana' });
        return;
      }

      // campaign is now a single object (not an array)
      const createdCampaign = campaign;

      // TODO: Add platforms and categories when tables are created
      // For now, skip these to avoid 404 errors
      console.log('🚀 Selected platforms:', selectedPlatforms);
      console.log('🚀 Selected categories:', selectedCategories);

      // // Add platforms
      // const platformData = selectedPlatforms.map(platformId => ({
      //   platform_id: platformId,
      //   content_type_ids: [1], // Default to 'post' for now
      //   posts_required: 1,
      //   budget_per_post: data.budget / selectedPlatforms.length,
      // }));

      // const { error: platformsError } = await addCampaignPlatforms(createdCampaign.id, platformData);

      // if (platformsError) {
      //   console.error('Error adding platforms:', platformsError);
      // }

      // // Add categories
      // const { error: categoriesError } = await addCampaignCategories(createdCampaign.id, selectedCategories);

      // if (categoriesError) {
      //   console.error('Error adding categories:', categoriesError);
      // }

      onSuccess(createdCampaign.id);
    } catch (error) {
      console.error('Error creating campaign:', error);
      setError('root', { message: 'Neočekivana greška' });
    } finally {
      setIsLoading(false);
    }
  };

  const togglePlatform = (platformId: number) => {
    setSelectedPlatforms(prev =>
      prev.includes(platformId)
        ? prev.filter(id => id !== platformId)
        : [...prev, platformId]
    );
  };

  const toggleCategory = (categoryId: number) => {
    setSelectedCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const toggleContentType = (contentTypeId: string) => {
    setSelectedContentTypes(prev => {
      const newSelection = prev.includes(contentTypeId)
        ? prev.filter(id => id !== contentTypeId)
        : [...prev, contentTypeId];

      // Update form value
      setValue('contentTypes', newSelection);
      return newSelection;
    });
  };

  // Step validation functions
  const validateStep1 = () => {
    const values = getValues();
    const errors = [];

    if (!values.title?.trim()) errors.push('Naslov kampanje je obavezan');
    if (!values.description?.trim()) errors.push('Opis kampanje je obavezan');
    if (!values.budget || values.budget <= 0)
      errors.push('Budžet mora biti veći od 0');
    if (!values.collaborationType) errors.push('Tip saradnje je obavezan');

    if (errors.length > 0) {
      setError('root', { message: errors.join(', ') });
      return false;
    }
    return true;
  };

  const validateStep2 = () => {
    if (selectedPlatforms.length === 0) {
      setError('root', { message: 'Morate izabrati najmanje jednu platformu' });
      return false;
    }
    if (selectedCategories.length === 0) {
      setError('root', {
        message: 'Morate izabrati najmanje jednu kategoriju',
      });
      return false;
    }
    if (selectedContentTypes.length === 0) {
      setError('root', {
        message: 'Morate izabrati najmanje jedan tip sadržaja',
      });
      return false;
    }
    return true;
  };

  const nextStep = () => {
    clearErrors('root');

    if (currentStep === 1 && !validateStep1()) return;
    if (currentStep === 2 && !validateStep2()) return;

    setCurrentStep(prev => Math.min(prev + 1, 3));
  };

  const prevStep = () => {
    clearErrors('root');
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  return (
    <TooltipProvider>
      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        {/* Progress indicator */}
        <div className="flex items-center justify-center space-x-4 mb-8">
          {[1, 2, 3].map(step => (
            <div key={step} className="flex items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step === currentStep
                    ? 'bg-primary text-primary-foreground'
                    : step < currentStep
                      ? 'bg-green-500 text-white'
                      : 'bg-muted text-muted-foreground'
                }`}
              >
                {step}
              </div>
              {step < 3 && (
                <div
                  className={`w-16 h-1 mx-2 ${
                    step < currentStep ? 'bg-green-500' : 'bg-muted'
                  }`}
                />
              )}
            </div>
          ))}
        </div>

        {/* Error display */}
        {errors.root && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-600 text-sm">{errors.root.message}</p>
          </div>
        )}

        {/* Step 1: Basic Information */}
        {currentStep === 1 && (
          <Card>
            <CardHeader>
              <CardTitle>Osnovne informacije</CardTitle>
              <CardDescription>
                Unesite osnovne podatke o vašoj kampanji
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title">Naziv kampanje *</Label>
                <Input
                  id="title"
                  {...register('title')}
                  placeholder="npr. Promocija novog proizvoda"
                />
                {errors.title && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.title.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="description">Opis kampanje *</Label>
                <Textarea
                  id="description"
                  {...register('description')}
                  placeholder="Detaljno opišite vašu kampanju, ciljeve i očekivanja..."
                  rows={4}
                />
                {errors.description && (
                  <p className="text-sm text-destructive mt-1">
                    {errors.description.message}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="budget">Budžet (KM) *</Label>
                  <Input
                    id="budget"
                    type="number"
                    {...register('budget', { valueAsNumber: true })}
                    placeholder="1000"
                  />
                  {errors.budget && (
                    <p className="text-sm text-destructive mt-1">
                      {errors.budget.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="location">Lokacija</Label>
                  <Input
                    id="location"
                    {...register('location')}
                    placeholder="Sarajevo, BiH"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="applicationDeadline">Rok za prijave</Label>
                <Input
                  id="applicationDeadline"
                  type="date"
                  {...register('applicationDeadline')}
                />
              </div>

              <div className="flex justify-end">
                <Button type="button" onClick={nextStep}>
                  Sledeći korak
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 2: Platforms and Categories */}
        {currentStep === 2 && (
          <Card>
            <CardHeader>
              <CardTitle>Platforme i kategorije</CardTitle>
              <CardDescription>
                Izaberite platforme i kategorije za vašu kampanju
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Platforms */}
              <div>
                <Label className="text-base font-medium">Platforme *</Label>
                <p className="text-sm text-muted-foreground mb-3">
                  Izaberite platforme na kojima želite da se objavi sadržaj
                </p>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {platforms.map(platform => (
                    <div
                      key={platform.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedPlatforms.includes(platform.id)
                          ? 'border-primary bg-primary/5'
                          : 'border-border hover:border-primary/50'
                      }`}
                      onClick={() => togglePlatform(platform.id)}
                    >
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{platform.icon}</span>
                        <span className="font-medium">{platform.name}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Categories */}
              <div>
                <Label className="text-base font-medium">Kategorije *</Label>
                <p className="text-sm text-muted-foreground mb-3">
                  Izaberite kategorije koje najbolje opisuju vašu kampanju
                </p>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {categories.map(category => (
                    <div
                      key={category.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedCategories.includes(category.id)
                          ? 'border-primary bg-primary/5'
                          : 'border-border hover:border-primary/50'
                      }`}
                      onClick={() => toggleCategory(category.id)}
                    >
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{category.icon}</span>
                        <span className="font-medium">{category.name}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Content Types */}
              <div>
                <Label className="text-base font-medium">
                  Tipovi sadržaja *
                </Label>
                <p className="text-sm text-muted-foreground mb-3">
                  Izaberite tipove sadržaja koje želite da influenceri kreiraju
                </p>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {contentTypes.map(contentType => (
                    <div
                      key={contentType.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedContentTypes.includes(contentType.id)
                          ? 'border-primary bg-primary/5'
                          : 'border-border hover:border-primary/50'
                      }`}
                      onClick={() => toggleContentType(contentType.id)}
                    >
                      <div className="flex items-center justify-center">
                        <span className="font-medium text-sm">
                          {contentType.name}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
                {errors.contentTypes && (
                  <p className="text-sm text-destructive mt-2">
                    {errors.contentTypes.message}
                  </p>
                )}
              </div>

              <div className="flex justify-between">
                <Button type="button" variant="outline" onClick={prevStep}>
                  Prethodni korak
                </Button>
                <Button type="button" onClick={nextStep}>
                  Sledeći korak
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step 3: Advanced Settings */}
        {currentStep === 3 && (
          <Card>
            <CardHeader>
              <CardTitle>Dodatne postavke</CardTitle>
              <CardDescription>
                Podesite dodatne opcije za vašu kampanju
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Collaboration Type */}
              <div>
                <Label htmlFor="collaborationType">Tip saradnje</Label>
                <Select
                  onValueChange={value =>
                    setValue('collaborationType', value as any)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Izaberite tip saradnje" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="paid">Plaćena saradnja</SelectItem>
                    <SelectItem value="barter">Barter (razmena)</SelectItem>
                    <SelectItem value="hybrid">
                      Hibridna (plaćeno + proizvodi)
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Target Audience */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="minFollowers">Min. broj pratilaca</Label>
                  <Input
                    id="minFollowers"
                    type="number"
                    {...register('minFollowers', { valueAsNumber: true })}
                    placeholder="1000"
                  />
                </div>
                <div>
                  <Label htmlFor="maxFollowers">Max. broj pratilaca</Label>
                  <Input
                    id="maxFollowers"
                    type="number"
                    {...register('maxFollowers', { valueAsNumber: true })}
                    placeholder="100000"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="gender">Pol ciljne grupe</Label>
                  <Select
                    onValueChange={value => setValue('gender', value as any)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Svi" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Svi</SelectItem>
                      <SelectItem value="male">Muški</SelectItem>
                      <SelectItem value="female">Ženski</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="ageRangeMin">Min. godine</Label>
                  <Input
                    id="ageRangeMin"
                    type="number"
                    {...register('ageRangeMin', { valueAsNumber: true })}
                    placeholder="18"
                  />
                </div>
                <div>
                  <Label htmlFor="ageRangeMax">Max. godine</Label>
                  <Input
                    id="ageRangeMax"
                    type="number"
                    {...register('ageRangeMax', { valueAsNumber: true })}
                    placeholder="35"
                  />
                </div>
              </div>

              {/* Requirements and Deliverables */}
              <div>
                <Label htmlFor="requirements">Zahtevi</Label>
                <Textarea
                  id="requirements"
                  {...register('requirements')}
                  placeholder="Specifični zahtevi za influencere..."
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="deliverables">Očekivani rezultati</Label>
                <Textarea
                  id="deliverables"
                  {...register('deliverables')}
                  placeholder="Šta očekujete da dobijete..."
                  rows={3}
                />
              </div>

              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="contactEmail">Kontakt email</Label>
                  <Input
                    id="contactEmail"
                    type="email"
                    {...register('contactEmail')}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <Label htmlFor="contactPhone">Kontakt telefon</Label>
                  <Input
                    id="contactPhone"
                    {...register('contactPhone')}
                    placeholder="+387 61 123 456"
                  />
                </div>
              </div>

              {/* Hashtags and Restrictions */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="hashtags">Obavezni hashtag-ovi</Label>
                  <Input
                    id="hashtags"
                    {...register('hashtags')}
                    placeholder="#brend, #kampanja, #promocija"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Odvojite zarezom
                  </p>
                </div>
                <div>
                  <Label htmlFor="doNotMention">Ne spominjati</Label>
                  <Input
                    id="doNotMention"
                    {...register('doNotMention')}
                    placeholder="konkurenti, određeni brendovi..."
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Odvojite zarezom
                  </p>
                </div>
              </div>

              {/* Checkboxes */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="showBusinessName"
                    {...register('showBusinessName')}
                  />
                  <Label htmlFor="showBusinessName" className="text-sm">
                    Prikaži naziv firme u kampanji
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="isFeatured" {...register('isFeatured')} />
                  <Label htmlFor="isFeatured" className="text-sm">
                    Istakni kampanju (dodatna naknada)
                  </Label>
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Istaknute kampanje se prikazuju na vrhu liste</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              </div>

              {/* Error message */}
              {errors.root && (
                <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
                  <p className="text-sm text-destructive">
                    {errors.root.message}
                  </p>
                </div>
              )}

              {/* Action buttons */}
              <div className="flex justify-between">
                <Button type="button" variant="outline" onClick={prevStep}>
                  Prethodni korak
                </Button>
                <div className="space-x-2">
                  {onCancel && (
                    <Button type="button" variant="outline" onClick={onCancel}>
                      Otkaži
                    </Button>
                  )}
                  <Button
                    type="submit"
                    disabled={isLoading || isSubmitting}
                    onClick={() => console.log('🔥 Submit button clicked!')}
                  >
                    {(isLoading || isSubmitting) && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    {submitButtonText || 'Kreiraj kampanju'}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </form>
    </TooltipProvider>
  );
}
