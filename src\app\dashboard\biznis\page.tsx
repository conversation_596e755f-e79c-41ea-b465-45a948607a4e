'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { getBusiness } from '@/lib/profiles';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Loader2,
  Users,
  TrendingUp,
  DollarSign,
  Plus,
  FileText,
  Edit,
  Target,
  BarChart3,
  Calendar,
  CheckCircle,
  ArrowUpRight,
  Sparkles,
} from 'lucide-react';

export default function BiznisDashboardPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [business, setBusiness] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadBusiness();
    }
  }, [user]);

  const loadBusiness = async () => {
    try {
      setLoading(true);
      const { data, error } = await getBusiness(user!.id);

      if (error || !data) {
        // Business profile doesn't exist, redirect to profile creation
        router.push('/profil/kreiranje/biznis');
        return;
      }

      setBusiness(data);
    } catch (err) {
      console.error('Error loading business data:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout requiredUserType="business">
        <div className="space-y-8">
          {/* Welcome Section Skeleton */}
          <div className="space-y-2">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-96" />
          </div>

          {/* Stats Cards Skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map(i => (
              <Card key={i}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-4 rounded" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-8 w-16 mb-2" />
                  <Skeleton className="h-3 w-32" />
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Profile Card Skeleton */}
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-4 w-48" />
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  {[1, 2, 3].map(i => (
                    <Skeleton key={i} className="h-4 w-full" />
                  ))}
                </div>
                <div className="space-y-2">
                  {[1, 2, 3].map(i => (
                    <Skeleton key={i} className="h-4 w-full" />
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout requiredUserType="business">
      <div className="space-y-8">
        {/* Welcome Section */}
        <div className="relative overflow-hidden rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white">
          <div className="relative z-10">
            <div className="flex items-center gap-2 mb-2">
              <Sparkles className="h-6 w-6" />
              <h1 className="text-3xl font-bold">
                Dobrodošli, {business?.company_name || 'Biznis'}!
              </h1>
            </div>
            <p className="text-blue-100 mb-4">
              Upravljajte vašim kampanjama i pronađite savršene influencere
            </p>
            <div className="flex items-center gap-4">
              <Badge
                variant="secondary"
                className="bg-white/20 text-white border-white/30"
              >
                <CheckCircle className="h-3 w-3 mr-1" />
                Profil kompletiran
              </Badge>
              <Badge
                variant="secondary"
                className="bg-white/20 text-white border-white/30"
              >
                <Target className="h-3 w-3 mr-1" />
                Spreman za kampanje
              </Badge>
            </div>
          </div>
          <div className="absolute top-0 right-0 -mt-4 -mr-4 h-24 w-24 rounded-full bg-white/10" />
          <div className="absolute bottom-0 left-0 -mb-8 -ml-8 h-32 w-32 rounded-full bg-white/5" />
        </div>

        {/* Enhanced Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="relative overflow-hidden border-l-4 border-l-blue-500 hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-blue-700">
                Aktivne kampanje
              </CardTitle>
              <div className="p-2 bg-blue-100 rounded-full">
                <TrendingUp className="h-4 w-4 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-900">0</div>
              <p className="text-xs text-muted-foreground mb-2">
                Trenutno nema aktivnih kampanja
              </p>
              <Progress value={0} className="h-1" />
              <div className="flex items-center mt-2 text-xs text-muted-foreground">
                <ArrowUpRight className="h-3 w-3 mr-1" />
                Kreiraj prvu kampanju
              </div>
            </CardContent>
          </Card>

          <Card className="relative overflow-hidden border-l-4 border-l-green-500 hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-green-700">
                Ukupno potrošeno
              </CardTitle>
              <div className="p-2 bg-green-100 rounded-full">
                <DollarSign className="h-4 w-4 text-green-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-900">0 KM</div>
              <p className="text-xs text-muted-foreground mb-2">
                +0% od prošlog mjeseca
              </p>
              <Progress value={0} className="h-1" />
              <div className="flex items-center mt-2 text-xs text-muted-foreground">
                <BarChart3 className="h-3 w-3 mr-1" />
                Budžet spreman za korišćenje
              </div>
            </CardContent>
          </Card>

          <Card className="relative overflow-hidden border-l-4 border-l-purple-500 hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-purple-700">
                Saradnje
              </CardTitle>
              <div className="p-2 bg-purple-100 rounded-full">
                <Users className="h-4 w-4 text-purple-600" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-900">0</div>
              <p className="text-xs text-muted-foreground mb-2">
                Broj završenih saradnji
              </p>
              <Progress value={0} className="h-1" />
              <div className="flex items-center mt-2 text-xs text-muted-foreground">
                <Calendar className="h-3 w-3 mr-1" />
                Počni sa prvom saradnjom
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Company Profile */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="h-4 w-4 text-blue-600" />
                </div>
                Profil firme
              </CardTitle>
              <CardDescription>
                Pregled informacija o vašoj firmi
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="hover:bg-blue-50 hover:border-blue-200"
              onClick={() => router.push('/dashboard/biznis/profile')}
            >
              <Edit className="mr-2 h-4 w-4" />
              Uredi profil
            </Button>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Osnovne informacije
                  </h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                      <span className="text-sm text-muted-foreground">
                        Username:
                      </span>
                      <Badge variant="outline">
                        @{business?.profiles?.username}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                      <span className="text-sm text-muted-foreground">
                        Lokacija:
                      </span>
                      <span className="text-sm font-medium">
                        {business?.profiles?.location || 'Nije navedeno'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                      <span className="text-sm text-muted-foreground">
                        Website:
                      </span>
                      {business?.profiles?.website_url ? (
                        <a
                          href={business.profiles.website_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-primary hover:underline text-sm"
                        >
                          {business.profiles.website_url}
                        </a>
                      ) : (
                        <span className="text-sm text-muted-foreground">
                          Nije navedeno
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    Detalji firme
                  </h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                      <span className="text-sm text-muted-foreground">
                        Industrija:
                      </span>
                      <span className="text-sm font-medium">
                        {business?.industry || 'Nije navedeno'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                      <span className="text-sm text-muted-foreground">
                        Veličina:
                      </span>
                      <span className="text-sm font-medium">
                        {business?.company_size || 'Nije navedeno'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                      <span className="text-sm text-muted-foreground">
                        Budžet:
                      </span>
                      <Badge
                        variant="secondary"
                        className="bg-green-100 text-green-800"
                      >
                        {business?.budget_range
                          ? `${business.budget_range} KM`
                          : 'Nije navedeno'}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {business?.profiles?.bio && (
              <>
                <Separator />
                <div>
                  <h4 className="font-medium mb-3 flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    Opis firme
                  </h4>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {business.profiles.bio}
                    </p>
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Enhanced Quick Actions */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Sparkles className="h-4 w-4 text-orange-600" />
              </div>
              Brze akcije
            </CardTitle>
            <CardDescription>Šta želite da radite danas?</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Button
                className="h-auto p-6 flex flex-col items-start space-y-3 bg-gradient-to-br from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 transition-all duration-200 transform hover:scale-105"
                onClick={() => router.push('/campaigns/create')}
              >
                <div className="flex items-center space-x-2">
                  <Plus className="h-5 w-5" />
                  <span className="font-medium">Kreiraj kampanju</span>
                </div>
                <span className="text-sm text-blue-100 text-left">
                  Kreiraj novu kampanju i pronađi influencere
                </span>
              </Button>
              <Button
                variant="outline"
                className="h-auto p-6 flex flex-col items-start space-y-3 border-2 hover:border-purple-200 hover:bg-purple-50 transition-all duration-200 transform hover:scale-105"
                onClick={() => router.push('/marketplace/influencers')}
              >
                <div className="flex items-center space-x-2 text-purple-700">
                  <Users className="h-5 w-5" />
                  <span className="font-medium">Pronađi influencere</span>
                </div>
                <span className="text-sm text-muted-foreground text-left">
                  Pregledaj i direktno angažuj influencere
                </span>
              </Button>
              <Button
                variant="outline"
                className="h-auto p-6 flex flex-col items-start space-y-3 border-2 hover:border-green-200 hover:bg-green-50 transition-all duration-200 transform hover:scale-105"
                onClick={() => router.push('/dashboard/campaigns')}
              >
                <div className="flex items-center space-x-2 text-green-700">
                  <FileText className="h-5 w-5" />
                  <span className="font-medium">Moje kampanje</span>
                </div>
                <span className="text-sm text-muted-foreground text-left">
                  Upravljajte svojim kampanjama i statusima
                </span>
              </Button>
              <Button
                variant="outline"
                className="h-auto p-6 flex flex-col items-start space-y-3 border-2 hover:border-orange-200 hover:bg-orange-50 transition-all duration-200 transform hover:scale-105"
                onClick={() => router.push('/dashboard/biznis/applications')}
              >
                <div className="flex items-center space-x-2 text-orange-700">
                  <FileText className="h-5 w-5" />
                  <span className="font-medium">Pregled aplikacija</span>
                </div>
                <span className="text-sm text-muted-foreground text-left">
                  Upravljajte aplikacijama na vaše kampanje
                </span>
              </Button>
              <Button
                variant="outline"
                className="h-auto p-6 flex flex-col items-start space-y-3 border-2 hover:border-blue-200 hover:bg-blue-50 transition-all duration-200 transform hover:scale-105"
                onClick={() => router.push('/dashboard/biznis/profile')}
              >
                <div className="flex items-center space-x-2 text-blue-700">
                  <Edit className="h-5 w-5" />
                  <span className="font-medium">Uredi profil</span>
                </div>
                <span className="text-sm text-muted-foreground text-left">
                  Ažuriraj informacije o vašoj firmi
                </span>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Getting Started */}
        <Card className="bg-gradient-to-br from-gray-50 to-gray-100 border-2 border-gray-200 hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div className="p-2 bg-green-100 rounded-lg">
                <Target className="h-4 w-4 text-green-600" />
              </div>
              Početak rada
            </CardTitle>
            <CardDescription>
              Evo kako možete početi sa InfluConnect platformom
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg">
                  <span className="text-sm font-bold text-white">1</span>
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 mb-1">
                    Kreirajte vašu prvu kampanju
                  </h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    Definirajte ciljeve, budžet i tip sadržaja koji želite
                  </p>
                  <Progress value={0} className="h-2" />
                  <p className="text-xs text-muted-foreground mt-1">
                    Nije započeto
                  </p>
                </div>
              </div>
              <Separator className="my-4" />
              <div className="flex items-start space-x-4">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg">
                  <span className="text-sm font-bold text-white">2</span>
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 mb-1">
                    Pregledajte aplikacije
                  </h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    Influenceri će aplicirati na vašu kampanju sa prijedlozima
                  </p>
                  <Progress value={0} className="h-2" />
                  <p className="text-xs text-muted-foreground mt-1">
                    Čeka prvu kampanju
                  </p>
                </div>
              </div>
              <Separator className="my-4" />
              <div className="flex items-start space-x-4">
                <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg">
                  <span className="text-sm font-bold text-white">3</span>
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 mb-1">
                    Izaberite i platite
                  </h4>
                  <p className="text-sm text-muted-foreground mb-2">
                    Izaberite najbolje influencere i sigurno platite kroz našu
                    platformu
                  </p>
                  <Progress value={0} className="h-2" />
                  <p className="text-xs text-muted-foreground mt-1">
                    Čeka odabir influencera
                  </p>
                </div>
              </div>

              <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-900">
                    Savjet za početak
                  </span>
                </div>
                <p className="text-sm text-blue-700">
                  Počnite sa manjom kampanjom da se upoznate sa platformom.
                  Možete uvijek proširiti budžet kasnije!
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
