'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useAuth } from '@/contexts/AuthContext';
import {
  getProfile,
  updateProfile,
  getInfluencer,
  updateInfluencer,
  getInfluencerPlatforms,
  getInfluencerCategories,
} from '@/lib/profiles';
import { supabase } from '@/lib/supabase';
import {
  Loader2,
  Save,
  User,
  Globe,
  Package,
  Eye,
  Instagram,
  Youtube,
  Plus,
} from 'lucide-react';
import { toast } from 'sonner';
import Link from 'next/link';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';

// TikTok icon component since it's not in Lucide
const TikTokIcon = ({ className }: { className?: string }) => (
  <svg
    className={className}
    viewBox="0 0 24 24"
    fill="currentColor"
  >
    <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z"/>
  </svg>
);
import { InfluencerCard } from '@/components/marketplace/InfluencerCard';
import type { InfluencerSearchResult } from '@/lib/marketplace';

const profileSchema = z.object({
  username: z.string().min(3, 'Username mora imati najmanje 3 karaktera'),
  bio: z
    .string()
    .max(500, 'Bio može imati maksimalno 500 karaktera')
    .optional(),
  location: z.string().optional(),
  instagram_handle: z.string().optional(),
  instagram_followers: z.number().min(0).optional(),
  tiktok_handle: z.string().optional(),
  tiktok_followers: z.number().min(0).optional(),
  youtube_handle: z.string().optional(),
  youtube_subscribers: z.number().min(0).optional(),
});

type ProfileForm = z.infer<typeof profileSchema>;

export default function InfluencerProfilePage() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [categories, setCategories] = useState<any[]>([]);
  const [profile, setProfile] = useState<any>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<ProfileForm>({
    resolver: zodResolver(profileSchema),
  });

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  // Watch form values for preview
  const watchedValues = watch();

  // Create preview data for InfluencerCard
  const createPreviewData = (): InfluencerSearchResult => {
    const totalFollowers = (watchedValues.instagram_followers || 0) +
                          (watchedValues.tiktok_followers || 0) +
                          (watchedValues.youtube_subscribers || 0);

    const platforms = [];
    if (watchedValues.instagram_followers && watchedValues.instagram_followers > 0) {
      platforms.push({
        platform_id: 1,
        platform_name: 'Instagram',
        platform_icon: '📷',
        handle: watchedValues.instagram_handle || '@username',
        followers_count: watchedValues.instagram_followers,
        is_verified: false,
      });
    }
    if (watchedValues.tiktok_followers && watchedValues.tiktok_followers > 0) {
      platforms.push({
        platform_id: 2,
        platform_name: 'TikTok',
        platform_icon: '🎵',
        handle: watchedValues.tiktok_handle || '@username',
        followers_count: watchedValues.tiktok_followers,
        is_verified: false,
      });
    }
    if (watchedValues.youtube_subscribers && watchedValues.youtube_subscribers > 0) {
      platforms.push({
        platform_id: 3,
        platform_name: 'YouTube',
        platform_icon: '📺',
        handle: watchedValues.youtube_handle || '@username',
        followers_count: watchedValues.youtube_subscribers,
        is_verified: false,
      });
    }

    return {
      id: user?.id || 'preview',
      username: watchedValues.username || 'username',
      full_name: watchedValues.username || 'Vaše ime',
      avatar_url: '',
      bio: watchedValues.bio || 'Vaš bio...',
      location: watchedValues.location || 'Lokacija',
      gender: 'prefer_not_to_say',
      age: 0,
      is_verified: false,
      categories: [],
      platforms,
      pricing: [],
      min_price: 0,
      max_price: 0,
      total_followers: totalFollowers,
      relevance_score: 1.0,
      average_rating: 0,
      total_reviews: 0,
    };
  };

  const loadData = async () => {
    try {
      setLoading(true);
      const { data: profileData, error: profileError } = await getProfile(
        user!.id
      );
      if (profileError) {
        toast.error('Greška pri učitavanju profila');
        return;
      }

      // Load influencer data
      const { data: influencerData, error: influencerError } =
        await getInfluencer(user!.id);
      if (influencerError) {
        toast.error('Greška pri učitavanju influencer podataka');
        return;
      }

      // Load influencer platforms
      const { data: platformsData, error: platformsError } = await getInfluencerPlatforms(user!.id);

      // Extract platform data
      const instagramPlatform = platformsData?.find(p => p.platform_id === 1);
      const tiktokPlatform = platformsData?.find(p => p.platform_id === 2);
      const youtubePlatform = platformsData?.find(p => p.platform_id === 3);

      // Load influencer categories
      const { data: categoriesData, error: categoriesError } = await getInfluencerCategories(user!.id);
      if (!categoriesError && categoriesData) {
        setCategories(categoriesData);
      }

      // Set profile data for additional info section
      setProfile(profileData);


      // Popuni formu sa postojećim podacima
      reset({
        username: profileData?.username || '',
        bio: profileData?.bio || '',
        location: profileData?.city || '', // Use city instead of location
        instagram_handle: instagramPlatform?.handle || '',
        instagram_followers: instagramPlatform?.followers_count || 0,
        tiktok_handle: tiktokPlatform?.handle || '',
        tiktok_followers: tiktokPlatform?.followers_count || 0,
        youtube_handle: youtubePlatform?.handle || '',
        youtube_subscribers: youtubePlatform?.followers_count || 0,
      });
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Greška pri učitavanju podataka');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: ProfileForm) => {
    try {
      setSaving(true);

      // Update profile
      const { error: profileError } = await updateProfile(user!.id, {
        username: data.username,
        bio: data.bio || null,
        city: data.location || null, // Use city instead of location
      });

      if (profileError) {
        toast.error('Greška pri ažuriranju profila');
        return;
      }

      // Update platform data using the new platform system
      const platformUpdates = [
        { platform_id: 1, handle: data.instagram_handle, followers_count: data.instagram_followers },
        { platform_id: 2, handle: data.tiktok_handle, followers_count: data.tiktok_followers },
        { platform_id: 3, handle: data.youtube_handle, followers_count: data.youtube_subscribers },
      ];

      for (const platform of platformUpdates) {
        if (platform.handle) {
          // Upsert platform data
          const { error: platformError } = await supabase
            .from('influencer_platforms')
            .upsert({
              influencer_id: user!.id,
              platform_id: platform.platform_id,
              handle: platform.handle,
              followers_count: platform.followers_count || 0,
              is_active: true,
            });

          if (platformError) {
            console.error('Error updating platform:', platformError);
          }
        }
      }

      toast.success('Profil je uspješno ažuriran!');
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Greška pri ažuriranju profila');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Profil</h1>
          <p className="text-muted-foreground">
            Upravljajte vašim profilom i informacijama
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Marketplace Preview */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Eye className="h-5 w-5" />
                  <CardTitle>Kako vas vide brendovi</CardTitle>
                </div>
                <Badge variant="secondary">Preview</Badge>
              </div>
              <CardDescription>
                Ovako će vaš profil izgledati u marketplace-u
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="max-w-sm mx-auto">
                <InfluencerCard influencer={createPreviewData()} />
              </div>

            </CardContent>
          </Card>

          {/* Basic Information */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <User className="h-5 w-5" />
                <CardTitle>Osnovne informacije</CardTitle>
              </div>
              <CardDescription>
                Ažurirajte vaše osnovne informacije
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="username">Username</Label>
                  <Input
                    id="username"
                    {...register('username')}
                    placeholder="Unesite username"
                  />
                  {errors.username && (
                    <p className="text-sm text-destructive">
                      {errors.username.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location">Lokacija</Label>
                  <Input
                    id="location"
                    {...register('location')}
                    placeholder="Unesite lokaciju"
                  />
                  {errors.location && (
                    <p className="text-sm text-destructive">
                      {errors.location.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="bio">Bio</Label>
                <Textarea
                  id="bio"
                  {...register('bio')}
                  placeholder="Opišite sebe..."
                  rows={4}
                />
                {errors.bio && (
                  <p className="text-sm text-destructive">
                    {errors.bio.message}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Social Media */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Globe className="h-5 w-5" />
                <CardTitle>Društvene mreže</CardTitle>
              </div>
              <CardDescription>
                Dodajte vaše profile na društvenim mrežama
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Instagram */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Instagram className="h-5 w-5 text-pink-500" />
                  <Label className="text-base font-medium">Instagram</Label>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ml-7">
                  <div className="space-y-2">
                    <Label htmlFor="instagram_handle">Handle</Label>
                    <Input
                      id="instagram_handle"
                      {...register('instagram_handle')}
                      placeholder="@username"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="instagram_followers">Pratilaca</Label>
                    <Input
                      id="instagram_followers"
                      type="number"
                      {...register('instagram_followers', {
                        valueAsNumber: true,
                      })}
                      placeholder="0"
                    />
                  </div>
                </div>
              </div>

              {/* TikTok */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <TikTokIcon className="h-5 w-5" />
                  <Label className="text-base font-medium">TikTok</Label>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ml-7">
                  <div className="space-y-2">
                    <Label htmlFor="tiktok_handle">Handle</Label>
                    <Input
                      id="tiktok_handle"
                      {...register('tiktok_handle')}
                      placeholder="@username"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="tiktok_followers">Pratilaca</Label>
                    <Input
                      id="tiktok_followers"
                      type="number"
                      {...register('tiktok_followers', {
                        valueAsNumber: true,
                      })}
                      placeholder="0"
                    />
                  </div>
                </div>
              </div>

              {/* YouTube */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Youtube className="h-5 w-5 text-red-500" />
                  <Label className="text-base font-medium">YouTube</Label>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ml-7">
                  <div className="space-y-2">
                    <Label htmlFor="youtube_handle">Handle</Label>
                    <Input
                      id="youtube_handle"
                      {...register('youtube_handle')}
                      placeholder="@username"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="youtube_subscribers">Pretplatnika</Label>
                    <Input
                      id="youtube_subscribers"
                      type="number"
                      {...register('youtube_subscribers', {
                        valueAsNumber: true,
                      })}
                      placeholder="0"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Separator />

          {/* Pricing Packages Link */}
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Package className="h-5 w-5" />
                <CardTitle>Paketi i cijene</CardTitle>
              </div>
              <CardDescription>
                Upravljajte vašim paketima i cijenama
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground">
                  Kreirajte i upravljajte vašim paketima za različite platforme
                  i tipove sadržaja.
                </p>
                <Link href="/dashboard/influencer/pricing">
                  <Button variant="outline">
                    <Package className="h-4 w-4 mr-2" />
                    Upravljaj paketima
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          {/* Additional Profile Information */}
          <Card>
            <CardHeader>
              <CardTitle>Dodatne informacije</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Age */}
              {profile?.age && (
                <div className="space-y-2">
                  <Label>Godine</Label>
                  <div className="text-sm text-muted-foreground">
                    {profile.age} godina
                  </div>
                </div>
              )}

              {/* Gender */}
              {profile?.gender && (
                <div className="space-y-2">
                  <Label>Pol</Label>
                  <div className="text-sm text-muted-foreground">
                    {profile.gender === 'musko' ? 'Muško' : profile.gender === 'zensko' ? 'Žensko' : 'Ostalo'}
                  </div>
                </div>
              )}

              {/* Categories */}
              {categories && categories.length > 0 && (
                <div className="space-y-2">
                  <Label>Kategorije</Label>
                  <div className="flex flex-wrap gap-2">
                    {categories.map((category, index) => (
                      <Badge key={index} variant="secondary">
                        {category.category_name}
                      </Badge>
                    ))}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Za izmenu kategorija i pola, kontaktirajte podršku.
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button type="submit" disabled={saving}>
              {saving ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Čuvanje...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Sačuvaj promjene
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
}
