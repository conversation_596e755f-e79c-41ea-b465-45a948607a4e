'use client';

import { DesktopNavigation } from './DesktopNavigation';
import { MobileBottomNavigation } from './MobileBottomNavigation';

interface ResponsiveNavigationProps {
  userType: 'influencer' | 'business';
}

export function ResponsiveNavigation({ userType }: ResponsiveNavigationProps) {
  return (
    <>
      {/* Desktop Navigation - prikazuje se samo na desktop uređajima */}
      <DesktopNavigation userType={userType} />

      {/* Mobile Bottom Navigation - prikazuje se samo na mobile uređajima */}
      <MobileBottomNavigation userType={userType} />
    </>
  );
}
