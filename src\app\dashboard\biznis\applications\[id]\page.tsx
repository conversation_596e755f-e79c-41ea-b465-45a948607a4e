'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  ArrowLeft,
  CheckCircle,
  XCircle,
  User,
  Calendar,
  DollarSign,
  ExternalLink,
  MessageSquare,
  Star,
  Clock,
} from 'lucide-react';
import Link from 'next/link';
import {
  getCampaignApplication,
  updateApplicationStatus,
} from '@/lib/campaigns';
import { ChatEnableButton } from '@/components/chat/ChatEnableButton';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import {
  getJobCompletionByCampaignApplication,
  type JobCompletion,
} from '@/lib/job-completions';
import { ApproveJobModal } from '@/components/job-completion/ApproveJobModal';
import { RejectJobModal } from '@/components/job-completion/RejectJobModal';

interface ApplicationDetails {
  id: string;
  campaign_id: string;
  influencer_id: string;
  status: 'pending' | 'accepted' | 'rejected';
  proposed_price: number;
  delivery_timeframe: string;
  portfolio_links: string[];
  relevant_experience: string;
  audience_insights: string;
  additional_services: string[];
  created_at: string;
  campaign: {
    id: string;
    title: string;
    description: string;
    budget: number;
    requirements: string;
    deliverables: string;
    business_id: string;
  };
  influencer: {
    id: string;
    full_name: string;
    username: string;
    avatar_url: string;
    bio: string;
    followers_count: number;
    categories: string[];
    platforms: Array<{
      platform_name: string;
      handle: string;
      followers_count: number;
    }>;
  };
}

export default function ApplicationDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const [application, setApplication] = useState<ApplicationDetails | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');
  const [showRejectionForm, setShowRejectionForm] = useState(false);
  const [jobCompletion, setJobCompletion] = useState<JobCompletion | null>(
    null
  );
  const [isLoadingJobCompletion, setIsLoadingJobCompletion] = useState(false);
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);

  useEffect(() => {
    if (!user || !params.id) return;

    const loadApplication = async () => {
      try {
        const { data, error } = await getCampaignApplication(
          params.id as string
        );
        if (error) {
          console.error('Error loading application:', error);
          return;
        }
        setApplication(data);

        // Load job completion if application is accepted
        if (data && data.status === 'accepted') {
          loadJobCompletion(params.id as string);
        }
      } catch (error) {
        console.error('Error loading application:', error);
      } finally {
        setLoading(false);
      }
    };

    loadApplication();
  }, [user, params.id]);

  const loadJobCompletion = async (applicationId: string) => {
    setIsLoadingJobCompletion(true);
    try {
      const { data, error } =
        await getJobCompletionByCampaignApplication(applicationId);
      if (error) {
        console.error('Error loading job completion:', error);
        return;
      }
      setJobCompletion(data);
    } catch (error) {
      console.error('Error loading job completion:', error);
    } finally {
      setIsLoadingJobCompletion(false);
    }
  };

  const handleStatusUpdate = async (
    status: 'accepted' | 'rejected',
    reason?: string
  ) => {
    if (!application) return;

    setUpdating(true);
    try {
      const { error } = await updateApplicationStatus(
        application.id,
        status,
        reason
      );
      if (error) {
        console.error('Error updating application:', error);
        return;
      }

      setApplication(prev => (prev ? { ...prev, status } : null));
      setShowRejectionForm(false);
      setRejectionReason('');
    } catch (error) {
      console.error('Error updating application:', error);
    } finally {
      setUpdating(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Na čekanju';
      case 'accepted':
        return 'Prihvaćeno';
      case 'rejected':
        return 'Odbačeno';
      default:
        return status;
    }
  };

  const getJobStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary">Čeka se početak rada</Badge>;
      case 'submitted':
        return <Badge variant="outline">Poslano na pregled</Badge>;
      case 'approved':
        return <Badge variant="default">Odobreno</Badge>;
      case 'rejected':
        return <Badge variant="destructive">Odbačeno</Badge>;
      case 'completed':
        return <Badge variant="default">Završeno</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (!application) {
    return (
      <DashboardLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Aplikacija nije pronađena
            </h1>
            <Link href="/dashboard/biznis/applications">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Nazad na aplikacije
              </Button>
            </Link>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/biznis/applications">
              <Button variant="outline" size="sm">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Nazad
              </Button>
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Detalji aplikacije
              </h1>
              <p className="text-gray-600">
                Pregled aplikacije za kampanju "{application.campaign.title}"
              </p>
            </div>
          </div>

          <Badge className={getStatusColor(application.status)}>
            <span>{getStatusText(application.status)}</span>
          </Badge>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Influencer Info */}
            <Card>
              <CardHeader>
                <CardTitle>Informacije o influenceru</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-start space-x-4">
                  <Avatar className="h-16 w-16">
                    <AvatarImage src={application.influencer.avatar_url} />
                    <AvatarFallback>
                      {(application.influencer.full_name || application.influencer.username || '?').charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>

                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="text-xl font-semibold">
                        {application.influencer.full_name}
                      </h3>
                      <Badge variant="secondary">
                        @{application.influencer.username}
                      </Badge>
                    </div>

                    <p className="text-gray-600 mb-4">
                      {application.influencer.bio}
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">
                          Kategorije
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {application.influencer.categories?.map(
                            (category, index) => (
                              <Badge key={index} variant="outline">
                                {category}
                              </Badge>
                            )
                          )}
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">
                          Platforme
                        </h4>
                        <div className="space-y-2">
                          {application.influencer.platforms?.map(
                            (platform, index) => (
                              <div
                                key={index}
                                className="flex items-center justify-between text-sm"
                              >
                                <span className="font-medium">
                                  {platform.platform_name}
                                </span>
                                <span className="text-gray-600">
                                  @{platform.handle} •{' '}
                                  {platform.followers_count?.toLocaleString()}{' '}
                                  pratilaca
                                </span>
                              </div>
                            )
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Application Details */}
            <Card>
              <CardHeader>
                <CardTitle>Detalji aplikacije</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="flex items-center space-x-2">
                    <DollarSign className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Predložena cijena</p>
                      <p className="font-semibold">
                        {application.proposed_price} KM
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Calendar className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Vrijeme isporuke</p>
                      <p className="font-semibold">
                        {application.delivery_timeframe}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Clock className="h-5 w-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-600">Datum aplikacije</p>
                      <p className="font-semibold">
                        {new Date(application.created_at).toLocaleDateString(
                          'bs-BA'
                        )}
                      </p>
                    </div>
                  </div>
                </div>

                <Separator />

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">
                    Relevantno iskustvo
                  </h4>
                  <p className="text-gray-700 whitespace-pre-wrap">
                    {application.relevant_experience}
                  </p>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">
                    Insights o publici
                  </h4>
                  <p className="text-gray-700 whitespace-pre-wrap">
                    {application.audience_insights}
                  </p>
                </div>

                {application.portfolio_links &&
                  application.portfolio_links.length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">
                        Portfolio linkovi
                      </h4>
                      <div className="space-y-2">
                        {application.portfolio_links.map((link, index) => (
                          <a
                            key={index}
                            href={link}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center space-x-2 text-blue-600 hover:text-blue-800"
                          >
                            <ExternalLink className="h-4 w-4" />
                            <span>{link}</span>
                          </a>
                        ))}
                      </div>
                    </div>
                  )}

                {application.additional_services &&
                  application.additional_services.length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">
                        Dodatne usluge
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {application.additional_services.map(
                          (service, index) => (
                            <Badge key={index} variant="outline">
                              {service}
                            </Badge>
                          )
                        )}
                      </div>
                    </div>
                  )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Campaign Info */}
            <Card>
              <CardHeader>
                <CardTitle>Informacije o kampanji</CardTitle>
              </CardHeader>
              <CardContent>
                <h3 className="font-semibold text-lg mb-2">
                  {application.campaign.title}
                </h3>
                <p className="text-gray-600 mb-4">
                  {application.campaign.description}
                </p>

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Budget:</span>
                    <span className="font-medium">
                      {application.campaign.budget} KM
                    </span>
                  </div>
                </div>

                <Separator className="my-4" />

                <div>
                  <h4 className="font-medium mb-2">Zahtjevi</h4>
                  <p className="text-sm text-gray-600">
                    {application.campaign.requirements}
                  </p>
                </div>

                <div className="mt-4">
                  <h4 className="font-medium mb-2">Deliverables</h4>
                  <p className="text-sm text-gray-600">
                    {application.campaign.deliverables}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            {application.status === 'pending' && (
              <Card>
                <CardHeader>
                  <CardTitle>Akcije</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    onClick={() => handleStatusUpdate('accepted')}
                    disabled={updating}
                    className="w-full"
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Prihvati aplikaciju
                  </Button>

                  <Button
                    variant="destructive"
                    onClick={() => setShowRejectionForm(true)}
                    disabled={updating}
                    className="w-full"
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Odbaci aplikaciju
                  </Button>

                  {showRejectionForm && (
                    <div className="space-y-3 pt-4 border-t">
                      <Label htmlFor="rejection-reason">
                        Razlog odbacivanja (opcionalno)
                      </Label>
                      <Textarea
                        id="rejection-reason"
                        value={rejectionReason}
                        onChange={e => setRejectionReason(e.target.value)}
                        placeholder="Objasnite zašto odbacujete ovu aplikaciju..."
                        rows={3}
                      />
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() =>
                            handleStatusUpdate('rejected', rejectionReason)
                          }
                          disabled={updating}
                        >
                          Potvrdi odbacivanje
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setShowRejectionForm(false);
                            setRejectionReason('');
                          }}
                        >
                          Otkaži
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Chat Enable Button */}
            {application.status === 'accepted' && (
              <Card>
                <CardHeader>
                  <CardTitle>Komunikacija</CardTitle>
                </CardHeader>
                <CardContent>
                  <ChatEnableButton
                    applicationId={application.id}
                    businessId={application.campaign.business_id}
                    influencerId={application.influencer_id}
                    onChatEnabled={() => {
                      console.log(
                        'Chat enabled for application:',
                        application.id
                      );
                    }}
                  />
                </CardContent>
              </Card>
            )}

            {/* Job Completion */}
            {application.status === 'accepted' && (
              <Card>
                <CardHeader>
                  <CardTitle>Status rada</CardTitle>
                </CardHeader>
                <CardContent>
                  {isLoadingJobCompletion ? (
                    <div className="animate-pulse space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  ) : jobCompletion ? (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Status:</span>
                        {getJobStatusBadge(jobCompletion.status || 'pending')}
                      </div>

                      {jobCompletion.submitted_at && (
                        <div>
                          <span className="text-sm font-medium">Poslano:</span>
                          <p className="text-sm text-gray-600">
                            {new Date(
                              jobCompletion.submitted_at
                            ).toLocaleDateString('bs-BA')}
                          </p>
                        </div>
                      )}

                      {jobCompletion.submission_notes && (
                        <div>
                          <span className="text-sm font-medium">
                            Napomene influencera:
                          </span>
                          <p className="text-sm text-gray-600 mt-1">
                            {jobCompletion.submission_notes}
                          </p>
                        </div>
                      )}

                      {jobCompletion.status === 'submitted' && (
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            onClick={() => setShowApproveModal(true)}
                            className="flex-1"
                          >
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Odobri
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => setShowRejectModal(true)}
                            className="flex-1"
                          >
                            <XCircle className="h-4 w-4 mr-2" />
                            Odbaci
                          </Button>
                        </div>
                      )}

                      {jobCompletion.business_notes && (
                        <div>
                          <span className="text-sm font-medium">
                            Vaše napomene:
                          </span>
                          <p className="text-sm text-gray-600 mt-1">
                            {jobCompletion.business_notes}
                          </p>
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-600">
                      Influencer još uvek nije počeo rad na ovom projektu.
                    </p>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Modals */}
        {jobCompletion && (
          <>
            <ApproveJobModal
              isOpen={showApproveModal}
              onClose={() => setShowApproveModal(false)}
              jobCompletionId={jobCompletion.id}
              influencerName={
                jobCompletion.influencer_profile?.full_name ||
                jobCompletion.influencer_profile?.username ||
                'Influencer'
              }
              onSuccess={() => {
                setShowApproveModal(false);
                loadJobCompletion(application.id);
              }}
            />
            <RejectJobModal
              isOpen={showRejectModal}
              onClose={() => setShowRejectModal(false)}
              jobCompletionId={jobCompletion.id}
              influencerName={
                jobCompletion.influencer_profile?.full_name ||
                jobCompletion.influencer_profile?.username ||
                'Influencer'
              }
              onSuccess={() => {
                setShowRejectModal(false);
                loadJobCompletion(application.id);
              }}
            />
          </>
        )}
      </div>
    </DashboardLayout>
  );
}
